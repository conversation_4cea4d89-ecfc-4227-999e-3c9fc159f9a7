{"version": 3, "sources": [], "sections": [{"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/ai/genkit.ts"], "sourcesContent": ["import {genkit} from 'genkit';\r\nimport {googleAI} from '@genkit-ai/googleai';\r\n\r\nexport const ai = genkit({\r\n  plugins: [googleAI()],\r\n  model: 'googleai/gemini-2.0-flash',\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEO,MAAM,KAAK,CAAA,GAAA,uIAAA,CAAA,SAAM,AAAD,EAAE;IACvB,SAAS;QAAC,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD;KAAI;IACrB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/ai/flows/get-suggested-users-flow.ts"], "sourcesContent": ["'use server';\r\n/**\r\n * @fileOverview An AI flow to get user suggestions.\r\n *\r\n * - getSuggestedUsers - A function that returns a list of suggested users.\r\n * - GetSuggestedUsersInput - The input type for the getSuggestedUsers function.\r\n * - GetSuggestedUsersOutput - The return type for the getSuggestedUsers function.\r\n */\r\n\r\nimport {ai} from '@/ai/genkit';\r\nimport {z} from 'zod';\r\n\r\nconst SuggestedUserSchema = z.object({\r\n  username: z.string().describe('The username of the suggested user.'),\r\n  avatarUrl: z.string().describe('The URL of the user\\'s avatar image.'),\r\n  reason: z.string().describe('A short reason why this user is being suggested.'),\r\n});\r\nexport type SuggestedUser = z.infer<typeof SuggestedUserSchema>;\r\n\r\nconst GetSuggestedUsersInputSchema = z.object({\r\n  user: z.object({\r\n    username: z.string().describe(\"The current user's username.\"),\r\n    interests: z.string().describe(\"A summary of the user's interests, like 'photography, travel, nature'.\"),\r\n  }),\r\n});\r\nexport type GetSuggestedUsersInput = z.infer<typeof GetSuggestedUsersInputSchema>;\r\n\r\nconst GetSuggestedUsersOutputSchema = z.array(SuggestedUserSchema);\r\nexport type GetSuggestedUsersOutput = z.infer<typeof GetSuggestedUsersOutputSchema>;\r\n\r\nexport async function getSuggestedUsers(input: GetSuggestedUsersInput): Promise<GetSuggestedUsersOutput> {\r\n  return getSuggestedUsersFlow(input);\r\n}\r\n\r\nconst prompt = ai.definePrompt({\r\n  name: 'getSuggestedUsersPrompt',\r\n  input: {schema: GetSuggestedUsersInputSchema},\r\n  output: {schema: GetSuggestedUsersOutputSchema},\r\n  prompt: `You are a helpful assistant that suggests other users for someone to follow on a social media platform.\r\nThe platform is focused on creative content like images and photography.\r\nBased on the current user's profile and interests, suggest 4 other users they might want to follow.\r\nFor each suggested user, provide a username, an avatar URL, and a short, compelling reason why the current user might like them.\r\nThe suggested users should have creative and interesting work related to the current user's interests.\r\n\r\nDo not suggest the current user to themselves.\r\n\r\nCurrent user:\r\nUsername: {{{user.username}}}\r\nInterests: {{{user.interests}}}\r\n\r\nGenerate fictional but realistic-looking usernames.\r\nFor avatar URLs, use placeholder images from https://placehold.co/100x100.png.`,\r\n});\r\n\r\nconst getSuggestedUsersFlow = ai.defineFlow(\r\n  {\r\n    name: 'getSuggestedUsersFlow',\r\n    inputSchema: GetSuggestedUsersInputSchema,\r\n    outputSchema: GetSuggestedUsersOutputSchema,\r\n  },\r\n  async (input) => {\r\n    const {output} = await prompt(input);\r\n    return output || [];\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;;;AACA;;;;;;CAMC,GAED;AACA;;;;;;AAEA,MAAM,sBAAsB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B,QAAQ,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC9B;AAGA,MAAM,+BAA+B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACb,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC9B,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC;AACF;AAGA,MAAM,gCAAgC,oIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;AAGvC,eAAe,kBAAkB,KAA6B;IACnE,OAAO,sBAAsB;AAC/B;AAEA,MAAM,SAAS,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC7B,MAAM;IACN,OAAO;QAAC,QAAQ;IAA4B;IAC5C,QAAQ;QAAC,QAAQ;IAA6B;IAC9C,QAAQ,CAAC;;;;;;;;;;;;;8EAamE,CAAC;AAC/E;AAEA,MAAM,wBAAwB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACzC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO;IACL,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,OAAO;IAC9B,OAAO,UAAU,EAAE;AACrB;;;IAjCoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/.next-internal/server/app/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getSuggestedUsers as '40b11c9b700e2ac87cc1ab9113cdcba1a31b2bf8f4'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/app/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/app/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,8BACA", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}