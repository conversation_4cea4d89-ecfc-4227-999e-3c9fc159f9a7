{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/hooks/use-auth.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist, createJSONStorage } from \"zustand/middleware\";\r\nimport { User } from \"@/lib/types\";\r\nimport { supabase } from \"@/lib/supabase\";\r\nimport {\r\n  AuthError,\r\n  Session,\r\n  User as SupabaseUser,\r\n} from \"@supabase/supabase-js\";\r\nimport { useEffect } from \"react\";\r\n\r\ninterface AuthState {\r\n  user: User | null;\r\n  session: Session | null;\r\n  isAuthenticated: boolean;\r\n  isLoading: boolean;\r\n  error: string | null;\r\n  // Auth methods\r\n  signUp: (\r\n    email: string,\r\n    password: string,\r\n    username: string\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signIn: (\r\n    email: string,\r\n    password: string\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signInWithOAuth: (\r\n    provider: \"google\" | \"azure\" | \"discord\" | \"facebook\"\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signOut: () => Promise<void>;\r\n  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;\r\n  updateProfile: (updates: Partial<User>) => Promise<{ error: Error | null }>;\r\n  uploadAvatar: (\r\n    file: File\r\n  ) => Promise<{ url: string | null; error: Error | null }>;\r\n  // Internal methods\r\n  setSession: (session: Session | null) => void;\r\n  setUser: (user: User | null) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  setError: (error: string | null) => void;\r\n}\r\n\r\nconst useAuthStore = create<AuthState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      user: null,\r\n      session: null,\r\n      isAuthenticated: false,\r\n      isLoading: true,\r\n      error: null,\r\n\r\n      signUp: async (email: string, password: string, username: string) => {\r\n        set({ isLoading: true, error: null });\r\n\r\n        const { data, error } = await supabase.auth.signUp({\r\n          email,\r\n          password,\r\n          options: {\r\n            data: {\r\n              username,\r\n            },\r\n          },\r\n        });\r\n\r\n        if (error) {\r\n          set({ error: error.message, isLoading: false });\r\n          return { error };\r\n        }\r\n\r\n        // User will be created in the database via trigger\r\n        set({ isLoading: false });\r\n        return { error: null };\r\n      },\r\n\r\n      signIn: async (email: string, password: string) => {\r\n        set({ isLoading: true, error: null });\r\n\r\n        const { data, error } = await supabase.auth.signInWithPassword({\r\n          email,\r\n          password,\r\n        });\r\n\r\n        if (error) {\r\n          set({ error: error.message, isLoading: false });\r\n          return { error };\r\n        }\r\n\r\n        // Session will be handled by the auth state change listener\r\n        return { error: null };\r\n      },\r\n\r\n      signInWithOAuth: async (\r\n        provider: \"google\" | \"azure\" | \"discord\" | \"facebook\"\r\n      ) => {\r\n        set({ isLoading: true, error: null });\r\n\r\n        const { data, error } = await supabase.auth.signInWithOAuth({\r\n          provider,\r\n          options: {\r\n            redirectTo: `${window.location.origin}/auth/callback`,\r\n          },\r\n        });\r\n\r\n        if (error) {\r\n          set({ error: error.message, isLoading: false });\r\n          return { error };\r\n        }\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      signOut: async () => {\r\n        set({ isLoading: true });\r\n        await supabase.auth.signOut();\r\n        set({\r\n          user: null,\r\n          session: null,\r\n          isAuthenticated: false,\r\n          isLoading: false,\r\n          error: null,\r\n        });\r\n      },\r\n\r\n      resetPassword: async (email: string) => {\r\n        const { error } = await supabase.auth.resetPasswordForEmail(email, {\r\n          redirectTo: `${window.location.origin}/auth/reset-password`,\r\n        });\r\n\r\n        if (error) {\r\n          set({ error: error.message });\r\n          return { error };\r\n        }\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      updateProfile: async (updates: Partial<User>) => {\r\n        const { user } = get();\r\n        if (!user) {\r\n          const error = new Error(\"No user logged in\");\r\n          set({ error: error.message });\r\n          return { error };\r\n        }\r\n\r\n        set({ isLoading: true, error: null });\r\n\r\n        const { error } = await supabase\r\n          .from(\"users\")\r\n          .update(updates)\r\n          .eq(\"id\", user.id);\r\n\r\n        if (error) {\r\n          set({ error: error.message, isLoading: false });\r\n          return { error: new Error(error.message) };\r\n        }\r\n\r\n        // Update local user state\r\n        set({\r\n          user: { ...user, ...updates },\r\n          isLoading: false,\r\n        });\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      uploadAvatar: async (file: File) => {\r\n        const { user } = get();\r\n        if (!user) {\r\n          const error = new Error(\"No user logged in\");\r\n          return { url: null, error };\r\n        }\r\n\r\n        const fileExt = file.name.split(\".\").pop();\r\n        const fileName = `${user.id}/avatar.${fileExt}`;\r\n\r\n        const { error: uploadError } = await supabase.storage\r\n          .from(\"avatars\")\r\n          .upload(fileName, file, { upsert: true });\r\n\r\n        if (uploadError) {\r\n          return { url: null, error: new Error(uploadError.message) };\r\n        }\r\n\r\n        const { data } = supabase.storage\r\n          .from(\"avatars\")\r\n          .getPublicUrl(fileName);\r\n\r\n        const avatarUrl = data.publicUrl;\r\n\r\n        // Update user profile with new avatar URL\r\n        const { error: updateError } = await get().updateProfile({ avatarUrl });\r\n\r\n        if (updateError) {\r\n          return { url: null, error: updateError };\r\n        }\r\n\r\n        return { url: avatarUrl, error: null };\r\n      },\r\n\r\n      setSession: (session: Session | null) => {\r\n        set({ session, isAuthenticated: !!session });\r\n      },\r\n\r\n      setUser: (user: User | null) => {\r\n        set({ user, isAuthenticated: !!user, isLoading: false });\r\n      },\r\n\r\n      setLoading: (loading: boolean) => {\r\n        set({ isLoading: loading });\r\n      },\r\n\r\n      setError: (error: string | null) => {\r\n        set({ error });\r\n      },\r\n    }),\r\n    {\r\n      name: \"auth-storage\",\r\n      storage: createJSONStorage(() => localStorage),\r\n      partialize: (state) => ({\r\n        // Only persist user data, not session (Supabase handles session persistence)\r\n        user: state.user,\r\n      }),\r\n    }\r\n  )\r\n);\r\n\r\n// Helper function to fetch user profile from database\r\nconst fetchUserProfile = async (\r\n  supabaseUser: SupabaseUser\r\n): Promise<User | null> => {\r\n  const { data, error } = await supabase\r\n    .from(\"users\")\r\n    .select(\"*\")\r\n    .eq(\"id\", supabaseUser.id)\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error(\"Error fetching user profile:\", error);\r\n    return null;\r\n  }\r\n\r\n  return {\r\n    id: data.id,\r\n    username: data.username,\r\n    email: data.email,\r\n    avatarUrl: data.avatar_url,\r\n    bio: data.bio,\r\n    contacts: {\r\n      website: {\r\n        value: data.website_url || \"\",\r\n        isPublic: data.website_public || false,\r\n      },\r\n      phone: {\r\n        value: data.phone || \"\",\r\n        isPublic: data.phone_public || false,\r\n      },\r\n      messaging: {\r\n        platform: data.messaging_platform || \"\",\r\n        username: data.messaging_username || \"\",\r\n        isPublic: data.messaging_public || false,\r\n      },\r\n    },\r\n  };\r\n};\r\n\r\n// Custom hook to initialize and use the store\r\nexport const useAuth = () => {\r\n  const state = useAuthStore();\r\n\r\n  useEffect(() => {\r\n    // Initialize auth state\r\n    const initializeAuth = async () => {\r\n      const {\r\n        data: { session },\r\n      } = await supabase.auth.getSession();\r\n\r\n      if (session?.user) {\r\n        const userProfile = await fetchUserProfile(session.user);\r\n        useAuthStore.getState().setSession(session);\r\n        useAuthStore.getState().setUser(userProfile);\r\n      } else {\r\n        useAuthStore.getState().setLoading(false);\r\n      }\r\n    };\r\n\r\n    // Listen for auth changes\r\n    const {\r\n      data: { subscription },\r\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\r\n      console.log(\"Auth state changed:\", event, session);\r\n\r\n      if (session?.user) {\r\n        const userProfile = await fetchUserProfile(session.user);\r\n        useAuthStore.getState().setSession(session);\r\n        useAuthStore.getState().setUser(userProfile);\r\n      } else {\r\n        useAuthStore.getState().setSession(null);\r\n        useAuthStore.getState().setUser(null);\r\n      }\r\n    });\r\n\r\n    initializeAuth();\r\n\r\n    return () => {\r\n      subscription.unsubscribe();\r\n    };\r\n  }, []);\r\n\r\n  return state;\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;AAMA;AAXA;;;;;AA6CA,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,IACxB,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,SAAS;QACT,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,QAAQ,OAAO,OAAe,UAAkB;YAC9C,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ;oBACF;gBACF;YACF;YAEA,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;oBAAE,WAAW;gBAAM;gBAC7C,OAAO;oBAAE;gBAAM;YACjB;YAEA,mDAAmD;YACnD,IAAI;gBAAE,WAAW;YAAM;YACvB,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;oBAAE,WAAW;gBAAM;gBAC7C,OAAO;oBAAE;gBAAM;YACjB;YAEA,4DAA4D;YAC5D,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,iBAAiB,OACf;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;gBAC1D;gBACA,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBACvD;YACF;YAEA,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;oBAAE,WAAW;gBAAM;gBAC7C,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,SAAS;YACP,IAAI;gBAAE,WAAW;YAAK;YACtB,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC3B,IAAI;gBACF,MAAM;gBACN,SAAS;gBACT,iBAAiB;gBACjB,WAAW;gBACX,OAAO;YACT;QACF;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC;gBAC3B,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;gBACT,MAAM,QAAQ,IAAI,MAAM;gBACxB,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC;gBAC3B,OAAO;oBAAE;gBAAM;YACjB;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;oBAAE,WAAW;gBAAM;gBAC7C,OAAO;oBAAE,OAAO,IAAI,MAAM,MAAM,OAAO;gBAAE;YAC3C;YAEA,0BAA0B;YAC1B,IAAI;gBACF,MAAM;oBAAE,GAAG,IAAI;oBAAE,GAAG,OAAO;gBAAC;gBAC5B,WAAW;YACb;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,cAAc,OAAO;YACnB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;gBACT,MAAM,QAAQ,IAAI,MAAM;gBACxB,OAAO;oBAAE,KAAK;oBAAM;gBAAM;YAC5B;YAEA,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YACxC,MAAM,WAAW,GAAG,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS;YAE/C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAClD,IAAI,CAAC,WACL,MAAM,CAAC,UAAU,MAAM;gBAAE,QAAQ;YAAK;YAEzC,IAAI,aAAa;gBACf,OAAO;oBAAE,KAAK;oBAAM,OAAO,IAAI,MAAM,YAAY,OAAO;gBAAE;YAC5D;YAEA,MAAM,EAAE,IAAI,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC9B,IAAI,CAAC,WACL,YAAY,CAAC;YAEhB,MAAM,YAAY,KAAK,SAAS;YAEhC,0CAA0C;YAC1C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,MAAM,aAAa,CAAC;gBAAE;YAAU;YAErE,IAAI,aAAa;gBACf,OAAO;oBAAE,KAAK;oBAAM,OAAO;gBAAY;YACzC;YAEA,OAAO;gBAAE,KAAK;gBAAW,OAAO;YAAK;QACvC;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;gBAAS,iBAAiB,CAAC,CAAC;YAAQ;QAC5C;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;gBAAM,WAAW;YAAM;QACxD;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YACtB,6EAA6E;YAC7E,MAAM,MAAM,IAAI;QAClB,CAAC;AACH;AAIJ,sDAAsD;AACtD,MAAM,mBAAmB,OACvB;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;IAEA,OAAO;QACL,IAAI,KAAK,EAAE;QACX,UAAU,KAAK,QAAQ;QACvB,OAAO,KAAK,KAAK;QACjB,WAAW,KAAK,UAAU;QAC1B,KAAK,KAAK,GAAG;QACb,UAAU;YACR,SAAS;gBACP,OAAO,KAAK,WAAW,IAAI;gBAC3B,UAAU,KAAK,cAAc,IAAI;YACnC;YACA,OAAO;gBACL,OAAO,KAAK,KAAK,IAAI;gBACrB,UAAU,KAAK,YAAY,IAAI;YACjC;YACA,WAAW;gBACT,UAAU,KAAK,kBAAkB,IAAI;gBACrC,UAAU,KAAK,kBAAkB,IAAI;gBACrC,UAAU,KAAK,gBAAgB,IAAI;YACrC;QACF;IACF;AACF;AAGO,MAAM,UAAU;IACrB,MAAM,QAAQ;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wBAAwB;QACxB,MAAM,iBAAiB;YACrB,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAElC,IAAI,SAAS,MAAM;gBACjB,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;gBACvD,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;YAClC,OAAO;gBACL,aAAa,QAAQ,GAAG,UAAU,CAAC;YACrC;QACF;QAEA,0BAA0B;QAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;YAChD,QAAQ,GAAG,CAAC,uBAAuB,OAAO;YAE1C,IAAI,SAAS,MAAM;gBACjB,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;gBACvD,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;YAClC,OAAO;gBACL,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;YAClC;QACF;QAEA;QAEA,OAAO;YACL,aAAa,WAAW;QAC1B;IACF,GAAG,EAAE;IAEL,OAAO;AACT", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAvatar.displayName = AvatarPrimitive.Root.displayName\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport {cn} from '@/lib/utils';\r\n\r\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\r\n  ({className, ...props}, ref) => {\r\n    return (\r\n      <textarea\r\n        className={cn(\r\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nTextarea.displayName = 'Textarea';\r\n\r\nexport {Textarea};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 860, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/lib/supabase-posts.ts"], "sourcesContent": ["import { supabase, storage } from './supabase'\nimport type { Post } from './types'\n\n// Upload image to Supabase Storage\nexport const uploadImage = async (\n  file: File,\n  path: string\n): Promise<string> => {\n  try {\n    console.log('Uploading image to Supabase Storage...')\n    \n    const fileName = `${Date.now()}_${file.name}`\n    const filePath = `${path}/${fileName}`\n    \n    const { data, error } = await storage\n      .from('post-images')\n      .upload(filePath, file, {\n        cacheControl: '3600',\n        upsert: false\n      })\n\n    if (error) {\n      throw error\n    }\n\n    // Get public URL\n    const { data: { publicUrl } } = storage\n      .from('post-images')\n      .getPublicUrl(data.path)\n\n    console.log('✅ Image uploaded successfully:', publicUrl)\n    return publicUrl\n  } catch (error) {\n    console.error('❌ Error uploading image to Supabase Storage:', error)\n    throw new Error(`Failed to upload image: ${error}`)\n  }\n}\n\n// Upload base64 image to Supabase Storage\nexport const uploadBase64Image = async (\n  base64Data: string,\n  path: string\n): Promise<string> => {\n  try {\n    console.log('Converting and uploading base64 image to Supabase Storage...')\n\n    // Convert base64 to blob\n    const response = await fetch(base64Data)\n    const blob = await response.blob()\n\n    const fileName = `${Date.now()}.png`\n    const filePath = `${path}/${fileName}`\n\n    const { data, error } = await storage\n      .from('post-images')\n      .upload(filePath, blob, {\n        cacheControl: '3600',\n        upsert: false\n      })\n\n    if (error) {\n      throw error\n    }\n\n    // Get public URL\n    const { data: { publicUrl } } = storage\n      .from('post-images')\n      .getPublicUrl(data.path)\n\n    console.log('✅ Base64 image uploaded successfully:', publicUrl)\n    return publicUrl\n  } catch (error) {\n    console.error('❌ Error uploading base64 image to Supabase Storage:', error)\n    throw new Error(`Failed to upload base64 image: ${error}`)\n  }\n}\n\n// Add a new post\nexport const addPost = async (post: Omit<Post, 'id'>): Promise<string> => {\n  try {\n    // If imageUrl is base64, upload it to storage\n    let imageUrl = post.imageUrl\n    if (post.imageUrl.startsWith('data:')) {\n      imageUrl = await uploadBase64Image(\n        post.imageUrl,\n        `posts/${post.author.username}`\n      )\n    }\n\n    const postData = {\n      author_username: post.author.username,\n      author_avatar_url: post.author.avatarUrl,\n      image_url: imageUrl,\n      caption: post.caption,\n      likes: post.likes || 0,\n      comments: post.comments || 0,\n      created_at: new Date().toISOString()\n    }\n\n    const { data, error } = await supabase\n      .from('posts')\n      .insert([postData])\n      .select()\n      .single()\n\n    if (error) {\n      throw error\n    }\n\n    return data.id\n  } catch (error) {\n    console.error('Error adding post:', error)\n    throw error\n  }\n}\n\n// Get all posts\nexport const getAllPosts = async (): Promise<Post[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('posts')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      throw error\n    }\n\n    return data.map(convertSupabaseToPost)\n  } catch (error) {\n    console.error('Error fetching posts:', error)\n    throw error\n  }\n}\n\n// Get posts by user\nexport const getPostsByUser = async (username: string): Promise<Post[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('posts')\n      .select('*')\n      .eq('author_username', username)\n      .order('created_at', { ascending: false })\n\n    if (error) {\n      throw error\n    }\n\n    return data.map(convertSupabaseToPost)\n  } catch (error) {\n    console.error('Error fetching user posts:', error)\n    throw error\n  }\n}\n\n// Update a post\nexport const updatePost = async (updatedPost: Post): Promise<void> => {\n  try {\n    const { error } = await supabase\n      .from('posts')\n      .update({\n        caption: updatedPost.caption,\n        likes: updatedPost.likes,\n        comments: updatedPost.comments,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', updatedPost.id)\n\n    if (error) {\n      throw error\n    }\n  } catch (error) {\n    console.error('Error updating post:', error)\n    throw error\n  }\n}\n\n// Delete a post\nexport const deletePost = async (postId: string): Promise<void> => {\n  try {\n    const { error } = await supabase\n      .from('posts')\n      .delete()\n      .eq('id', postId)\n\n    if (error) {\n      throw error\n    }\n  } catch (error) {\n    console.error('Error deleting post:', error)\n    throw error\n  }\n}\n\n// Subscribe to all posts with real-time updates\nexport const subscribeToAllPosts = (callback: (posts: Post[]) => void) => {\n  console.log('Setting up real-time subscription for all posts...')\n  \n  // Initial fetch\n  getAllPosts().then(callback).catch(console.error)\n  \n  // Set up real-time subscription\n  const subscription = supabase\n    .channel('posts')\n    .on('postgres_changes', \n      { event: '*', schema: 'public', table: 'posts' },\n      () => {\n        // Refetch all posts when any change occurs\n        getAllPosts().then(callback).catch(console.error)\n      }\n    )\n    .subscribe()\n\n  // Return unsubscribe function\n  return () => {\n    console.log('Unsubscribing from posts real-time updates')\n    subscription.unsubscribe()\n  }\n}\n\n// Subscribe to user posts with real-time updates\nexport const subscribeToUserPosts = (\n  username: string,\n  callback: (posts: Post[]) => void\n) => {\n  console.log(`Setting up real-time subscription for ${username} posts...`)\n  \n  // Initial fetch\n  getPostsByUser(username).then(callback).catch(console.error)\n  \n  // Set up real-time subscription\n  const subscription = supabase\n    .channel(`user-posts-${username}`)\n    .on('postgres_changes',\n      { \n        event: '*', \n        schema: 'public', \n        table: 'posts',\n        filter: `author_username=eq.${username}`\n      },\n      () => {\n        // Refetch user posts when any change occurs\n        getPostsByUser(username).then(callback).catch(console.error)\n      }\n    )\n    .subscribe()\n\n  // Return unsubscribe function\n  return () => {\n    console.log(`Unsubscribing from ${username} posts real-time updates`)\n    subscription.unsubscribe()\n  }\n}\n\n// Convert Supabase row to Post type\nconst convertSupabaseToPost = (row: any): Post => ({\n  id: row.id,\n  author: {\n    username: row.author_username,\n    avatarUrl: row.author_avatar_url\n  },\n  imageUrl: row.image_url,\n  caption: row.caption,\n  likes: row.likes,\n  comments: row.comments,\n  createdAt: row.created_at\n})\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAIO,MAAM,cAAc,OACzB,MACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,WAAW,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,IAAI,EAAE;QAC7C,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,UAAU;QAEtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,UAAO,CAClC,IAAI,CAAC,eACL,MAAM,CAAC,UAAU,MAAM;YACtB,cAAc;YACd,QAAQ;QACV;QAEF,IAAI,OAAO;YACT,MAAM;QACR;QAEA,iBAAiB;QACjB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,sHAAA,CAAA,UAAO,CACpC,IAAI,CAAC,eACL,YAAY,CAAC,KAAK,IAAI;QAEzB,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,OAAO;IACpD;AACF;AAGO,MAAM,oBAAoB,OAC/B,YACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,yBAAyB;QACzB,MAAM,WAAW,MAAM,MAAM;QAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,MAAM,WAAW,GAAG,KAAK,GAAG,GAAG,IAAI,CAAC;QACpC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE,UAAU;QAEtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,UAAO,CAClC,IAAI,CAAC,eACL,MAAM,CAAC,UAAU,MAAM;YACtB,cAAc;YACd,QAAQ;QACV;QAEF,IAAI,OAAO;YACT,MAAM;QACR;QAEA,iBAAiB;QACjB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,sHAAA,CAAA,UAAO,CACpC,IAAI,CAAC,eACL,YAAY,CAAC,KAAK,IAAI;QAEzB,QAAQ,GAAG,CAAC,yCAAyC;QACrD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uDAAuD;QACrE,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,OAAO;IAC3D;AACF;AAGO,MAAM,UAAU,OAAO;IAC5B,IAAI;QACF,8CAA8C;QAC9C,IAAI,WAAW,KAAK,QAAQ;QAC5B,IAAI,KAAK,QAAQ,CAAC,UAAU,CAAC,UAAU;YACrC,WAAW,MAAM,kBACf,KAAK,QAAQ,EACb,CAAC,MAAM,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE;QAEnC;QAEA,MAAM,WAAW;YACf,iBAAiB,KAAK,MAAM,CAAC,QAAQ;YACrC,mBAAmB,KAAK,MAAM,CAAC,SAAS;YACxC,WAAW;YACX,SAAS,KAAK,OAAO;YACrB,OAAO,KAAK,KAAK,IAAI;YACrB,UAAU,KAAK,QAAQ,IAAI;YAC3B,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;YAAC;SAAS,EACjB,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,MAAM;QACR;QAEA,OAAO,KAAK,EAAE;IAChB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM;IACR;AACF;AAGO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,MAAM;QACR;QAEA,OAAO,KAAK,GAAG,CAAC;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,mBAAmB,UACtB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO;YACT,MAAM;QACR;QAEA,OAAO,KAAK,GAAG,CAAC;IAClB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC;YACN,SAAS,YAAY,OAAO;YAC5B,OAAO,YAAY,KAAK;YACxB,UAAU,YAAY,QAAQ;YAC9B,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,YAAY,EAAE;QAE1B,IAAI,OAAO;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,sBAAsB,CAAC;IAClC,QAAQ,GAAG,CAAC;IAEZ,gBAAgB;IAChB,cAAc,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,KAAK;IAEhD,gCAAgC;IAChC,MAAM,eAAe,sHAAA,CAAA,WAAQ,CAC1B,OAAO,CAAC,SACR,EAAE,CAAC,oBACF;QAAE,OAAO;QAAK,QAAQ;QAAU,OAAO;IAAQ,GAC/C;QACE,2CAA2C;QAC3C,cAAc,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,KAAK;IAClD,GAED,SAAS;IAEZ,8BAA8B;IAC9B,OAAO;QACL,QAAQ,GAAG,CAAC;QACZ,aAAa,WAAW;IAC1B;AACF;AAGO,MAAM,uBAAuB,CAClC,UACA;IAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,SAAS,SAAS,CAAC;IAExE,gBAAgB;IAChB,eAAe,UAAU,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,KAAK;IAE3D,gCAAgC;IAChC,MAAM,eAAe,sHAAA,CAAA,WAAQ,CAC1B,OAAO,CAAC,CAAC,WAAW,EAAE,UAAU,EAChC,EAAE,CAAC,oBACF;QACE,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ,CAAC,mBAAmB,EAAE,UAAU;IAC1C,GACA;QACE,4CAA4C;QAC5C,eAAe,UAAU,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,KAAK;IAC7D,GAED,SAAS;IAEZ,8BAA8B;IAC9B,OAAO;QACL,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS,wBAAwB,CAAC;QACpE,aAAa,WAAW;IAC1B;AACF;AAEA,oCAAoC;AACpC,MAAM,wBAAwB,CAAC,MAAmB,CAAC;QACjD,IAAI,IAAI,EAAE;QACV,QAAQ;YACN,UAAU,IAAI,eAAe;YAC7B,WAAW,IAAI,iBAAiB;QAClC;QACA,UAAU,IAAI,SAAS;QACvB,SAAS,IAAI,OAAO;QACpB,OAAO,IAAI,KAAK;QAChB,UAAU,IAAI,QAAQ;QACtB,WAAW,IAAI,UAAU;IAC3B,CAAC", "debugId": null}}, {"offset": {"line": 1123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/hooks/use-post-store.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport type { Post } from \"@/lib/types\";\r\nimport {\r\n  addPost as supabaseAddPost,\r\n  updatePost as supabaseUpdatePost,\r\n  subscribeToAllPosts,\r\n  getAllPosts,\r\n} from \"@/lib/supabase-posts\";\r\n\r\ninterface PostState {\r\n  posts: Post[];\r\n  isLoading: boolean;\r\n  error: string | null;\r\n  addPost: (post: Omit<Post, \"id\">) => Promise<void>;\r\n  updatePost: (updatedPost: Post) => Promise<void>;\r\n  setPosts: (posts: Post[]) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  setError: (error: string | null) => void;\r\n  initializePosts: () => Promise<void>;\r\n}\r\n\r\nexport const usePostStore = create<PostState>((set, get) => ({\r\n  posts: [],\r\n  isLoading: false,\r\n  error: null,\r\n\r\n  addPost: async (post) => {\r\n    try {\r\n      set({ isLoading: true, error: null });\r\n      const postId = await supabaseAddPost(post);\r\n      // The real-time listener will update the posts automatically\r\n      console.log(\"Post added successfully with ID:\", postId);\r\n    } catch (error) {\r\n      console.error(\"Error adding post:\", error);\r\n      set({ error: \"Failed to add post\" });\r\n    } finally {\r\n      set({ isLoading: false });\r\n    }\r\n  },\r\n\r\n  updatePost: async (updatedPost) => {\r\n    try {\r\n      set({ isLoading: true, error: null });\r\n      await supabaseUpdatePost(updatedPost);\r\n      // The real-time listener will update the posts automatically\r\n      console.log(\"Post updated successfully\");\r\n    } catch (error) {\r\n      console.error(\"Error updating post:\", error);\r\n      set({ error: \"Failed to update post\" });\r\n    } finally {\r\n      set({ isLoading: false });\r\n    }\r\n  },\r\n\r\n  setPosts: (posts) => set({ posts }),\r\n  setLoading: (loading) => set({ isLoading: loading }),\r\n  setError: (error) => set({ error }),\r\n\r\n  initializePosts: async () => {\r\n    try {\r\n      set({ isLoading: true, error: null });\r\n\r\n      // Set up real-time listener\r\n      const unsubscribe = subscribeToAllPosts((posts) => {\r\n        set({ posts, isLoading: false });\r\n      });\r\n\r\n      // Store unsubscribe function for cleanup\r\n      (get() as any).unsubscribe = unsubscribe;\r\n    } catch (error) {\r\n      console.error(\"Error initializing posts:\", error);\r\n      set({ error: \"Failed to load posts\", isLoading: false });\r\n\r\n      // Fallback to one-time fetch\r\n      try {\r\n        const posts = await getAllPosts();\r\n        set({ posts });\r\n      } catch (fallbackError) {\r\n        console.error(\"Fallback fetch also failed:\", fallbackError);\r\n      }\r\n    }\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;AAEA;AAEA;AAJA;;;AAuBO,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC3D,OAAO,EAAE;QACT,WAAW;QACX,OAAO;QAEP,SAAS,OAAO;YACd,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBACnC,MAAM,SAAS,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAe,AAAD,EAAE;gBACrC,6DAA6D;gBAC7D,QAAQ,GAAG,CAAC,oCAAoC;YAClD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,IAAI;oBAAE,OAAO;gBAAqB;YACpC,SAAU;gBACR,IAAI;oBAAE,WAAW;gBAAM;YACzB;QACF;QAEA,YAAY,OAAO;YACjB,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBACnC,MAAM,CAAA,GAAA,+HAAA,CAAA,aAAkB,AAAD,EAAE;gBACzB,6DAA6D;gBAC7D,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,IAAI;oBAAE,OAAO;gBAAwB;YACvC,SAAU;gBACR,IAAI;oBAAE,WAAW;gBAAM;YACzB;QACF;QAEA,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAClD,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,iBAAiB;YACf,IAAI;gBACF,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAEnC,4BAA4B;gBAC5B,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE,CAAC;oBACvC,IAAI;wBAAE;wBAAO,WAAW;oBAAM;gBAChC;gBAEA,yCAAyC;gBACxC,MAAc,WAAW,GAAG;YAC/B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,IAAI;oBAAE,OAAO;oBAAwB,WAAW;gBAAM;gBAEtD,6BAA6B;gBAC7B,IAAI;oBACF,MAAM,QAAQ,MAAM,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD;oBAC9B,IAAI;wBAAE;oBAAM;gBACd,EAAE,OAAO,eAAe;oBACtB,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/new-post-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useRef, useEffect, useCallback } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>alogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogFooter,\r\n  DialogClose,\r\n  DialogDescription,\r\n} from \"@/components/ui/dialog\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Camera, Image as ImageIcon, Upload, X } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"./ui/alert\";\r\nimport type { User, Post } from \"@/lib/types\";\r\nimport { usePostStore } from \"@/hooks/use-post-store\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\n\r\ninterface NewPostDialogProps {\r\n  isOpen: boolean;\r\n  onOpenChange: (isOpen: boolean) => void;\r\n  user: User;\r\n}\r\n\r\nexport default function NewPostDialog({\r\n  isOpen,\r\n  onOpenChange,\r\n  user,\r\n}: NewPostDialogProps) {\r\n  const [image, setImage] = useState<string | null>(null);\r\n  const [caption, setCaption] = useState(\"\");\r\n  const [isCameraActive, setIsCameraActive] = useState(false);\r\n  const [hasCameraPermission, setHasCameraPermission] = useState<\r\n    boolean | null\r\n  >(null);\r\n  const [isDragOver, setIsDragOver] = useState(false);\r\n  const videoRef = useRef<HTMLVideoElement>(null);\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const { toast } = useToast();\r\n  const { addPost } = usePostStore();\r\n\r\n  const cleanupCamera = useCallback(() => {\r\n    if (videoRef.current && videoRef.current.srcObject) {\r\n      const stream = videoRef.current.srcObject as MediaStream;\r\n      stream.getTracks().forEach((track) => track.stop());\r\n      videoRef.current.srcObject = null;\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (isCameraActive) {\r\n      const getCameraPermission = async () => {\r\n        try {\r\n          const stream = await navigator.mediaDevices.getUserMedia({\r\n            video: true,\r\n          });\r\n          setHasCameraPermission(true);\r\n          if (videoRef.current) {\r\n            videoRef.current.srcObject = stream;\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error accessing camera:\", error);\r\n          setHasCameraPermission(false);\r\n          toast({\r\n            variant: \"destructive\",\r\n            title: \"Camera Access Denied\",\r\n            description:\r\n              \"Please enable camera permissions in your browser settings.\",\r\n          });\r\n          setIsCameraActive(false);\r\n        }\r\n      };\r\n      getCameraPermission();\r\n    } else {\r\n      cleanupCamera();\r\n    }\r\n\r\n    return () => {\r\n      cleanupCamera();\r\n    };\r\n  }, [isCameraActive, toast, cleanupCamera]);\r\n\r\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (event.target.files && event.target.files[0]) {\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => setImage(e.target?.result as string);\r\n      reader.readAsDataURL(event.target.files[0]);\r\n      setIsCameraActive(false);\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = (file: File) => {\r\n    if (file && file.type.startsWith(\"image/\")) {\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => setImage(e.target?.result as string);\r\n      reader.readAsDataURL(file);\r\n      setIsCameraActive(false);\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(true);\r\n  };\r\n\r\n  const handleDragLeave = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n    const files = e.dataTransfer.files;\r\n    if (files.length > 0) {\r\n      handleFileUpload(files[0]);\r\n    }\r\n  };\r\n\r\n  const handleCapture = () => {\r\n    if (videoRef.current && canvasRef.current) {\r\n      const video = videoRef.current;\r\n      const canvas = canvasRef.current;\r\n      canvas.width = video.videoWidth;\r\n      canvas.height = video.videoHeight;\r\n      const context = canvas.getContext(\"2d\");\r\n      context?.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);\r\n      const dataUrl = canvas.toDataURL(\"image/png\");\r\n      setImage(dataUrl);\r\n      setIsCameraActive(false);\r\n    }\r\n  };\r\n\r\n  const resetDialog = () => {\r\n    setImage(null);\r\n    setCaption(\"\");\r\n    setIsCameraActive(false);\r\n    cleanupCamera();\r\n  };\r\n\r\n  const handleClose = (open: boolean) => {\r\n    if (!open) {\r\n      resetDialog();\r\n    }\r\n    onOpenChange(open);\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (!image) {\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"No Image\",\r\n        description: \"Please select an image or take a photo.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const newPost = {\r\n        author: {\r\n          username: user.username,\r\n          avatarUrl: user.avatarUrl || \"\",\r\n        },\r\n        imageUrl: image,\r\n        caption: caption,\r\n        likes: 0,\r\n        comments: 0,\r\n        createdAt: new Date().toISOString(),\r\n      };\r\n\r\n      await addPost(newPost);\r\n\r\n      toast({\r\n        title: \"Post Created\",\r\n        description: \"Your new post has been added to the feed.\",\r\n      });\r\n\r\n      handleClose(false);\r\n    } catch (error) {\r\n      console.error(\"Error creating post:\", error);\r\n      toast({\r\n        variant: \"destructive\",\r\n        title: \"Error\",\r\n        description: \"Failed to create post. Please try again.\",\r\n      });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={handleClose}>\r\n      <DialogContent className=\"max-w-4xl lg:max-w-5xl w-[95vw]\">\r\n        <DialogHeader>\r\n          <DialogTitle>Create New Post</DialogTitle>\r\n          <DialogDescription>\r\n            Upload a photo or use your camera, write a caption, and share it\r\n            with your followers.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 py-4\">\r\n          <div\r\n            className={`flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-6 min-h-[28rem] lg:min-h-[32rem] w-full transition-all duration-200 ${\r\n              isDragOver\r\n                ? \"border-primary bg-primary/10 scale-[1.02]\"\r\n                : \"border-muted-foreground/25 bg-muted/50 hover:border-muted-foreground/40 hover:bg-muted/70\"\r\n            }`}\r\n            onDragOver={handleDragOver}\r\n            onDragLeave={handleDragLeave}\r\n            onDrop={handleDrop}\r\n          >\r\n            {!image && !isCameraActive && (\r\n              <div className=\"text-center space-y-6 w-full max-w-sm\">\r\n                <ImageIcon className=\"mx-auto h-16 w-16 text-muted-foreground\" />\r\n                <div className=\"space-y-2\">\r\n                  <p className=\"text-lg font-medium text-foreground\">\r\n                    {isDragOver ? \"Drop your image here\" : \"Upload your photo\"}\r\n                  </p>\r\n                  <p className=\"text-sm text-muted-foreground\">\r\n                    {isDragOver\r\n                      ? \"Release to upload\"\r\n                      : \"Drag and drop or click to select\"}\r\n                  </p>\r\n                </div>\r\n                <div className=\"flex flex-col sm:flex-row gap-3 w-full\">\r\n                  <Button\r\n                    onClick={() => fileInputRef.current?.click()}\r\n                    className=\"flex-1 h-11\"\r\n                  >\r\n                    <Upload className=\"mr-2 h-4 w-4\" /> Upload Image\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    onClick={() => setIsCameraActive(true)}\r\n                    className=\"flex-1 h-11\"\r\n                  >\r\n                    <Camera className=\"mr-2 h-4 w-4\" /> Use Camera\r\n                  </Button>\r\n                </div>\r\n                <Input\r\n                  ref={fileInputRef}\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  className=\"hidden\"\r\n                  onChange={handleImageUpload}\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {image && !isCameraActive && (\r\n              <div className=\"relative w-full h-full\">\r\n                <Image\r\n                  src={image}\r\n                  alt=\"Selected preview\"\r\n                  layout=\"fill\"\r\n                  objectFit=\"contain\"\r\n                  className=\"rounded-md\"\r\n                />\r\n                <Button\r\n                  variant=\"destructive\"\r\n                  size=\"icon\"\r\n                  className=\"absolute top-2 right-2 rounded-full h-8 w-8\"\r\n                  onClick={() => setImage(null)}\r\n                >\r\n                  <X className=\"h-4 w-4\" />\r\n                </Button>\r\n              </div>\r\n            )}\r\n\r\n            {isCameraActive && (\r\n              <div className=\"w-full h-full flex flex-col items-center justify-center space-y-4 p-4\">\r\n                <video\r\n                  ref={videoRef}\r\n                  className=\"w-full h-auto max-h-[20rem] rounded-lg shadow-lg\"\r\n                  autoPlay\r\n                  muted\r\n                  playsInline\r\n                />\r\n                <canvas ref={canvasRef} className=\"hidden\" />\r\n                {hasCameraPermission === false && (\r\n                  <Alert variant=\"destructive\" className=\"max-w-sm\">\r\n                    <AlertTitle>Camera Access Denied</AlertTitle>\r\n                    <AlertDescription>\r\n                      Please enable camera permissions in your browser settings\r\n                      to use this feature.\r\n                    </AlertDescription>\r\n                  </Alert>\r\n                )}\r\n                <div className=\"flex flex-col sm:flex-row gap-3 w-full max-w-sm\">\r\n                  <Button\r\n                    onClick={handleCapture}\r\n                    disabled={!hasCameraPermission}\r\n                    className=\"flex-1 h-11\"\r\n                  >\r\n                    Capture Photo\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    onClick={() => setIsCameraActive(false)}\r\n                    className=\"flex-1 h-11\"\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex items-center gap-4\">\r\n              <Avatar>\r\n                <AvatarImage src={user.avatarUrl} />\r\n                <AvatarFallback>\r\n                  {user.username.charAt(0).toUpperCase()}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <span className=\"font-semibold\">{user.username}</span>\r\n            </div>\r\n            <div>\r\n              <Label htmlFor=\"caption\" className=\"sr-only\">\r\n                Caption\r\n              </Label>\r\n              <Textarea\r\n                id=\"caption\"\r\n                placeholder=\"Write a caption...\"\r\n                value={caption}\r\n                onChange={(e) => setCaption(e.target.value)}\r\n                rows={10}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <DialogFooter>\r\n          <DialogClose asChild>\r\n            <Button type=\"button\" variant=\"secondary\">\r\n              Cancel\r\n            </Button>\r\n          </DialogClose>\r\n          <Button type=\"button\" onClick={handleSubmit}>\r\n            Share\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAtBA;;;;;;;;;;;;;;AA8Be,SAAS,cAAc,EACpC,MAAM,EACN,YAAY,EACZ,IAAI,EACe;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE3D;IACF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;IAE/B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,CAAC,SAAS,EAAE;YAClD,MAAM,SAAS,SAAS,OAAO,CAAC,SAAS;YACzC,OAAO,SAAS,GAAG,OAAO,CAAC,CAAC,QAAU,MAAM,IAAI;YAChD,SAAS,OAAO,CAAC,SAAS,GAAG;QAC/B;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,MAAM,sBAAsB;gBAC1B,IAAI;oBACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;wBACvD,OAAO;oBACT;oBACA,uBAAuB;oBACvB,IAAI,SAAS,OAAO,EAAE;wBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;oBAC/B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,2BAA2B;oBACzC,uBAAuB;oBACvB,MAAM;wBACJ,SAAS;wBACT,OAAO;wBACP,aACE;oBACJ;oBACA,kBAAkB;gBACpB;YACF;YACA;QACF,OAAO;YACL;QACF;QAEA,OAAO;YACL;QACF;IACF,GAAG;QAAC;QAAgB;QAAO;KAAc;IAEzC,MAAM,oBAAoB,CAAC;QACzB,IAAI,MAAM,MAAM,CAAC,KAAK,IAAI,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YAC/C,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC,IAAM,SAAS,EAAE,MAAM,EAAE;YAC1C,OAAO,aAAa,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE;YAC1C,kBAAkB;QACpB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YAC1C,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC,IAAM,SAAS,EAAE,MAAM,EAAE;YAC1C,OAAO,aAAa,CAAC;YACrB,kBAAkB;QACpB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QACd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,SAAS,OAAO,IAAI,UAAU,OAAO,EAAE;YACzC,MAAM,QAAQ,SAAS,OAAO;YAC9B,MAAM,SAAS,UAAU,OAAO;YAChC,OAAO,KAAK,GAAG,MAAM,UAAU;YAC/B,OAAO,MAAM,GAAG,MAAM,WAAW;YACjC,MAAM,UAAU,OAAO,UAAU,CAAC;YAClC,SAAS,UAAU,OAAO,GAAG,GAAG,MAAM,UAAU,EAAE,MAAM,WAAW;YACnE,MAAM,UAAU,OAAO,SAAS,CAAC;YACjC,SAAS;YACT,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,WAAW;QACX,kBAAkB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM;YACT;QACF;QACA,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO;YACV,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;YACA;QACF;QAEA,IAAI;YACF,MAAM,UAAU;gBACd,QAAQ;oBACN,UAAU,KAAK,QAAQ;oBACvB,WAAW,KAAK,SAAS,IAAI;gBAC/B;gBACA,UAAU;gBACV,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,MAAM,QAAQ;YAEd,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;gBACJ,SAAS;gBACT,OAAO;gBACP,aAAa;YACf;QACF;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAW,CAAC,kJAAkJ,EAC5J,aACI,8CACA,6FACJ;4BACF,YAAY;4BACZ,aAAa;4BACb,QAAQ;;gCAEP,CAAC,SAAS,CAAC,gCACV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,aAAa,yBAAyB;;;;;;8DAEzC,8OAAC;oDAAE,WAAU;8DACV,aACG,sBACA;;;;;;;;;;;;sDAGR,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,aAAa,OAAO,EAAE;oDACrC,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAErC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,kBAAkB;oDACjC,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAGvC,8OAAC,iIAAA,CAAA,QAAK;4CACJ,KAAK;4CACL,MAAK;4CACL,QAAO;4CACP,WAAU;4CACV,UAAU;;;;;;;;;;;;gCAKf,SAAS,CAAC,gCACT,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK;4CACL,KAAI;4CACJ,QAAO;4CACP,WAAU;4CACV,WAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,SAAS;sDAExB,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAKlB,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK;4CACL,WAAU;4CACV,QAAQ;4CACR,KAAK;4CACL,WAAW;;;;;;sDAEb,8OAAC;4CAAO,KAAK;4CAAW,WAAU;;;;;;wCACjC,wBAAwB,uBACvB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAc,WAAU;;8DACrC,8OAAC,iIAAA,CAAA,aAAU;8DAAC;;;;;;8DACZ,8OAAC,iIAAA,CAAA,mBAAgB;8DAAC;;;;;;;;;;;;sDAMtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,CAAC;oDACX,WAAU;8DACX;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS,IAAM,kBAAkB;oDACjC,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;sCAQT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;;8DACL,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,SAAS;;;;;;8DAChC,8OAAC,kIAAA,CAAA,iBAAc;8DACZ,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;sDAGxC,8OAAC;4CAAK,WAAU;sDAAiB,KAAK,QAAQ;;;;;;;;;;;;8CAEhD,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAU;;;;;;sDAG7C,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC1C,MAAM;;;;;;;;;;;;;;;;;;;;;;;;8BAMd,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,OAAO;sCAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,SAAQ;0CAAY;;;;;;;;;;;sCAI5C,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,SAAS;sCAAc;;;;;;;;;;;;;;;;;;;;;;;AAOvD", "debugId": null}}, {"offset": {"line": 1793, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Home, PlusSquare, Search, User as UserIcon } from 'lucide-react';\r\nimport { useAuth } from '@/hooks/use-auth';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Input } from './ui/input';\r\nimport NewPostDialog from './new-post-dialog';\r\nimport React from 'react';\r\n\r\nexport default function Header() {\r\n  const { user, logout } = useAuth();\r\n  const router = useRouter();\r\n  const [isNewPostOpen, setIsNewPostOpen] = React.useState(false);\r\n\r\n  const handleLogout = () => {\r\n    logout();\r\n    router.push('/login');\r\n  };\r\n  \r\n  const userInitial = user?.username ? user.username.charAt(0).toUpperCase() : '?';\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full border-b bg-card/80 backdrop-blur\">\r\n        <div className=\"container flex h-16 items-center justify-between max-w-5xl mx-auto px-4\">\r\n          <Link href=\"/\" className=\"text-2xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent\">\r\n            FireFrame\r\n          </Link>\r\n          <div className=\"hidden sm:flex flex-1 max-w-xs items-center relative\">\r\n             <Input placeholder=\"Search...\" className=\"pl-10\"/>\r\n             <Search className=\"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground\"/>\r\n          </div>\r\n          <nav className=\"flex items-center gap-2 sm:gap-4\">\r\n            <Button variant=\"ghost\" size=\"icon\" asChild>\r\n              <Link href=\"/\">\r\n                <Home className=\"h-6 w-6\" />\r\n                <span className=\"sr-only\">Home</span>\r\n              </Link>\r\n            </Button>\r\n            <Button variant=\"ghost\" size=\"icon\" onClick={() => setIsNewPostOpen(true)}>\r\n              <PlusSquare className=\"h-6 w-6\" />\r\n              <span className=\"sr-only\">New Post</span>\r\n            </Button>\r\n            {user && (\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button variant=\"ghost\" className=\"relative h-9 w-9 rounded-full\">\r\n                    <Avatar className=\"h-9 w-9\">\r\n                      <AvatarImage src={user.avatarUrl} alt={user.username} />\r\n                      <AvatarFallback>{userInitial}</AvatarFallback>\r\n                    </Avatar>\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\r\n                  <DropdownMenuLabel className=\"font-normal\">\r\n                    <div className=\"flex flex-col space-y-1\">\r\n                      <p className=\"text-sm font-medium leading-none\">{user.username}</p>\r\n                      <p className=\"text-xs leading-none text-muted-foreground\">\r\n                        {user.email}\r\n                      </p>\r\n                    </div>\r\n                  </DropdownMenuLabel>\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuItem asChild>\r\n                    <Link href={`/profile/${user.username}`}>\r\n                      <UserIcon className=\"mr-2 h-4 w-4\" />\r\n                      <span>Profile</span>\r\n                    </Link>\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuItem onClick={handleLogout}>\r\n                    Log out\r\n                  </DropdownMenuItem>\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            )}\r\n          </nav>\r\n        </div>\r\n      </header>\r\n      {user && <NewPostDialog isOpen={isNewPostOpen} onOpenChange={setIsNewPostOpen} user={user} />}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AAlBA;;;;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEzD,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,cAAc,MAAM,WAAW,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK;IAE7E,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAA0G;;;;;;sCAGnI,8OAAC;4BAAI,WAAU;;8CACZ,8OAAC,iIAAA,CAAA,QAAK;oCAAC,aAAY;oCAAY,WAAU;;;;;;8CACzC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;sCAErB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,OAAO;8CACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;8CAG9B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,SAAS,IAAM,iBAAiB;;sDAClE,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;gCAE3B,sBACC,8OAAC,4IAAA,CAAA,eAAY;;sDACX,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,OAAO;sDAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,8OAAC,kIAAA,CAAA,cAAW;4DAAC,KAAK,KAAK,SAAS;4DAAE,KAAK,KAAK,QAAQ;;;;;;sEACpD,8OAAC,kIAAA,CAAA,iBAAc;sEAAE;;;;;;;;;;;;;;;;;;;;;;sDAIvB,8OAAC,4IAAA,CAAA,sBAAmB;4CAAC,WAAU;4CAAO,OAAM;4CAAM,UAAU;;8DAC1D,8OAAC,4IAAA,CAAA,oBAAiB;oDAAC,WAAU;8DAC3B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAoC,KAAK,QAAQ;;;;;;0EAC9D,8OAAC;gEAAE,WAAU;0EACV,KAAK,KAAK;;;;;;;;;;;;;;;;;8DAIjB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8DACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,OAAO;8DACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,SAAS,EAAE,KAAK,QAAQ,EAAE;;0EACrC,8OAAC,kMAAA,CAAA,OAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8DACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDAAC,SAAS;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASpD,sBAAQ,8OAAC,2IAAA,CAAA,UAAa;gBAAC,QAAQ;gBAAe,cAAc;gBAAkB,MAAM;;;;;;;;AAG3F", "debugId": null}}, {"offset": {"line": 2106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/post-card.tsx"], "sourcesContent": ["import Image from 'next/image';\r\nimport { Heart, MessageCircle, Send, MoreHorizontal } from 'lucide-react';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  <PERSON>,\r\n  CardContent,\r\n  CardFooter,\r\n  CardHeader,\r\n} from '@/components/ui/card';\r\nimport type { Post } from '@/lib/types';\r\n\r\ninterface PostCardProps extends React.HTMLAttributes<HTMLDivElement> {\r\n  post: Post;\r\n}\r\n\r\nexport default function PostCard({ post, ...props }: PostCardProps) {\r\n  const authorInitial = post.author.username.charAt(0).toUpperCase();\r\n\r\n  return (\r\n    <Card className=\"w-full max-w-xl mx-auto rounded-xl shadow-lg overflow-hidden\" {...props}>\r\n      <CardHeader className=\"flex flex-row items-center gap-4 p-4\">\r\n        <Avatar>\r\n          <AvatarImage src={post.author.avatarUrl} alt={post.author.username} />\r\n          <AvatarFallback>{authorInitial}</AvatarFallback>\r\n        </Avatar>\r\n        <div className=\"font-semibold text-foreground\">{post.author.username}</div>\r\n        <Button variant=\"ghost\" size=\"icon\" className=\"ml-auto\">\r\n          <MoreHorizontal />\r\n        </Button>\r\n      </CardHeader>\r\n      <CardContent className=\"p-0\">\r\n        <div className=\"relative aspect-square w-full\">\r\n          <Image\r\n            src={post.imageUrl}\r\n            alt={`Post by ${post.author.username}`}\r\n            fill\r\n            className=\"object-cover\"\r\n            data-ai-hint=\"social media photo\"\r\n          />\r\n        </div>\r\n      </CardContent>\r\n      <CardFooter className=\"flex flex-col items-start p-4 gap-2\">\r\n        <div className=\"flex flex-row gap-2\">\r\n          <Button variant=\"ghost\" size=\"icon\">\r\n            <Heart className=\"h-6 w-6\" />\r\n          </Button>\r\n          <Button variant=\"ghost\" size=\"icon\">\r\n            <MessageCircle className=\"h-6 w-6\" />\r\n          </Button>\r\n          <Button variant=\"ghost\" size=\"icon\">\r\n            <Send className=\"h-6 w-6\" />\r\n          </Button>\r\n        </div>\r\n        <div className=\"text-sm font-semibold\">{post.likes.toLocaleString()} likes</div>\r\n        <div className=\"text-sm\">\r\n          <span className=\"font-semibold mr-2\">{post.author.username}</span>\r\n          <span>{post.caption}</span>\r\n        </div>\r\n        <div className=\"text-sm text-muted-foreground\">View all {post.comments.toLocaleString()} comments</div>\r\n      </CardFooter>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAYe,SAAS,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsB;IAChE,MAAM,gBAAgB,KAAK,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;IAEhE,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;QAAgE,GAAG,KAAK;;0BACtF,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,kIAAA,CAAA,SAAM;;0CACL,8OAAC,kIAAA,CAAA,cAAW;gCAAC,KAAK,KAAK,MAAM,CAAC,SAAS;gCAAE,KAAK,KAAK,MAAM,CAAC,QAAQ;;;;;;0CAClE,8OAAC,kIAAA,CAAA,iBAAc;0CAAE;;;;;;;;;;;;kCAEnB,8OAAC;wBAAI,WAAU;kCAAiC,KAAK,MAAM,CAAC,QAAQ;;;;;;kCACpE,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAO,WAAU;kCAC5C,cAAA,8OAAC,gNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;0BAGnB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,KAAK,QAAQ;wBAClB,KAAK,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,EAAE;wBACtC,IAAI;wBACJ,WAAU;wBACV,gBAAa;;;;;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;0CAE3B,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGpB,8OAAC;wBAAI,WAAU;;4BAAyB,KAAK,KAAK,CAAC,cAAc;4BAAG;;;;;;;kCACpE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAsB,KAAK,MAAM,CAAC,QAAQ;;;;;;0CAC1D,8OAAC;0CAAM,KAAK,OAAO;;;;;;;;;;;;kCAErB,8OAAC;wBAAI,WAAU;;4BAAgC;4BAAU,KAAK,QAAQ,CAAC,cAAc;4BAAG;;;;;;;;;;;;;;;;;;;AAIhG", "debugId": null}}, {"offset": {"line": 2413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2437, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/ai/flows/get-suggested-users-flow.ts"], "sourcesContent": ["'use server';\r\n/**\r\n * @fileOverview An AI flow to get user suggestions.\r\n *\r\n * - getSuggestedUsers - A function that returns a list of suggested users.\r\n * - GetSuggestedUsersInput - The input type for the getSuggestedUsers function.\r\n * - GetSuggestedUsersOutput - The return type for the getSuggestedUsers function.\r\n */\r\n\r\nimport {ai} from '@/ai/genkit';\r\nimport {z} from 'zod';\r\n\r\nconst SuggestedUserSchema = z.object({\r\n  username: z.string().describe('The username of the suggested user.'),\r\n  avatarUrl: z.string().describe('The URL of the user\\'s avatar image.'),\r\n  reason: z.string().describe('A short reason why this user is being suggested.'),\r\n});\r\nexport type SuggestedUser = z.infer<typeof SuggestedUserSchema>;\r\n\r\nconst GetSuggestedUsersInputSchema = z.object({\r\n  user: z.object({\r\n    username: z.string().describe(\"The current user's username.\"),\r\n    interests: z.string().describe(\"A summary of the user's interests, like 'photography, travel, nature'.\"),\r\n  }),\r\n});\r\nexport type GetSuggestedUsersInput = z.infer<typeof GetSuggestedUsersInputSchema>;\r\n\r\nconst GetSuggestedUsersOutputSchema = z.array(SuggestedUserSchema);\r\nexport type GetSuggestedUsersOutput = z.infer<typeof GetSuggestedUsersOutputSchema>;\r\n\r\nexport async function getSuggestedUsers(input: GetSuggestedUsersInput): Promise<GetSuggestedUsersOutput> {\r\n  return getSuggestedUsersFlow(input);\r\n}\r\n\r\nconst prompt = ai.definePrompt({\r\n  name: 'getSuggestedUsersPrompt',\r\n  input: {schema: GetSuggestedUsersInputSchema},\r\n  output: {schema: GetSuggestedUsersOutputSchema},\r\n  prompt: `You are a helpful assistant that suggests other users for someone to follow on a social media platform.\r\nThe platform is focused on creative content like images and photography.\r\nBased on the current user's profile and interests, suggest 4 other users they might want to follow.\r\nFor each suggested user, provide a username, an avatar URL, and a short, compelling reason why the current user might like them.\r\nThe suggested users should have creative and interesting work related to the current user's interests.\r\n\r\nDo not suggest the current user to themselves.\r\n\r\nCurrent user:\r\nUsername: {{{user.username}}}\r\nInterests: {{{user.interests}}}\r\n\r\nGenerate fictional but realistic-looking usernames.\r\nFor avatar URLs, use placeholder images from https://placehold.co/100x100.png.`,\r\n});\r\n\r\nconst getSuggestedUsersFlow = ai.defineFlow(\r\n  {\r\n    name: 'getSuggestedUsersFlow',\r\n    inputSchema: GetSuggestedUsersInputSchema,\r\n    outputSchema: GetSuggestedUsersOutputSchema,\r\n  },\r\n  async (input) => {\r\n    const {output} = await prompt(input);\r\n    return output || [];\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;;;;IA8BsB,oBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/lib/supabase-debug.ts"], "sourcesContent": ["import { supabase, logSupabaseConfig } from './supabase'\n\n// Test Supabase connection\nexport const testSupabaseConnection = async (): Promise<boolean> => {\n  try {\n    console.log('Testing Supabase connection...')\n    \n    // Test database connection by querying a simple table\n    const { data, error } = await supabase\n      .from('posts')\n      .select('count')\n      .limit(1)\n\n    if (error) {\n      // If posts table doesn't exist yet, that's okay - connection is still working\n      if (error.code === 'PGRST116' || error.message.includes('does not exist')) {\n        console.log('✅ Supabase connection successful! (Posts table not yet created)')\n        return true\n      }\n      throw error\n    }\n\n    console.log('✅ Supabase connection successful!')\n    console.log('Database query result:', data)\n    return true\n  } catch (error) {\n    console.error('❌ Supabase connection error:', error)\n    return false\n  }\n}\n\n// Test Supabase Storage connection\nexport const testSupabaseStorage = async (): Promise<boolean> => {\n  try {\n    console.log('Testing Supabase Storage connection...')\n    \n    // List buckets to test storage connection\n    const { data, error } = await supabase.storage.listBuckets()\n\n    if (error) {\n      throw error\n    }\n\n    console.log('✅ Supabase Storage connection successful!')\n    console.log('Available buckets:', data.map(bucket => bucket.name))\n    return true\n  } catch (error) {\n    console.error('❌ Supabase Storage connection error:', error)\n    return false\n  }\n}\n\n// Test Supabase Auth connection\nexport const testSupabaseAuth = async (): Promise<boolean> => {\n  try {\n    console.log('Testing Supabase Auth connection...')\n    \n    // Get current session to test auth connection\n    const { data: { session }, error } = await supabase.auth.getSession()\n\n    if (error) {\n      throw error\n    }\n\n    console.log('✅ Supabase Auth connection successful!')\n    console.log('Current session:', session ? 'Authenticated' : 'Not authenticated')\n    return true\n  } catch (error) {\n    console.error('❌ Supabase Auth connection error:', error)\n    return false\n  }\n}\n\n// Comprehensive Supabase connection test\nexport const testAllSupabaseServices = async (): Promise<{\n  database: boolean\n  storage: boolean\n  auth: boolean\n  overall: boolean\n}> => {\n  console.log('🧪 Running comprehensive Supabase service tests...')\n  \n  const results = {\n    database: await testSupabaseConnection(),\n    storage: await testSupabaseStorage(),\n    auth: await testSupabaseAuth(),\n    overall: false\n  }\n  \n  results.overall = results.database && results.storage && results.auth\n  \n  if (results.overall) {\n    console.log('🎉 All Supabase services are working correctly!')\n  } else {\n    console.log('⚠️ Some Supabase services may need attention:')\n    console.log('- Database:', results.database ? '✅' : '❌')\n    console.log('- Storage:', results.storage ? '✅' : '❌')\n    console.log('- Auth:', results.auth ? '✅' : '❌')\n  }\n  \n  return results\n}\n\n// Initialize Supabase debugging\nexport const initSupabaseDebug = async (): Promise<boolean> => {\n  console.log('🚀 Supabase Debug Mode')\n  logSupabaseConfig()\n  \n  const isConnected = await testSupabaseConnection()\n  \n  if (!isConnected) {\n    console.log('🚨 Supabase connection failed. Posts will not persist.')\n    console.log('💡 Make sure Supabase is properly configured and accessible.')\n    console.log('📋 Check your environment variables:')\n    console.log('   - NEXT_PUBLIC_SUPABASE_URL')\n    console.log('   - NEXT_PUBLIC_SUPABASE_ANON_KEY')\n    console.log('   - SUPABASE_SERVICE_ROLE_KEY (for server-side operations)')\n  }\n  \n  return isConnected\n}\n\n// Database schema validation\nexport const validateDatabaseSchema = async (): Promise<{\n  postsTable: boolean\n  usersTable: boolean\n  requiredColumns: boolean\n}> => {\n  try {\n    console.log('🔍 Validating database schema...')\n    \n    // Check if posts table exists and has required columns\n    const { data: postsData, error: postsError } = await supabase\n      .from('posts')\n      .select('id, author_username, author_avatar_url, image_url, caption, likes, comments, created_at')\n      .limit(1)\n    \n    // Check if users table exists\n    const { data: usersData, error: usersError } = await supabase\n      .from('users')\n      .select('id, username, email')\n      .limit(1)\n    \n    const results = {\n      postsTable: !postsError,\n      usersTable: !usersError,\n      requiredColumns: !postsError && postsData !== null\n    }\n    \n    console.log('Schema validation results:')\n    console.log('- Posts table:', results.postsTable ? '✅' : '❌')\n    console.log('- Users table:', results.usersTable ? '✅' : '❌')\n    console.log('- Required columns:', results.requiredColumns ? '✅' : '❌')\n    \n    if (!results.postsTable || !results.usersTable) {\n      console.log('💡 Run the database schema SQL from the migration guide to create missing tables.')\n    }\n    \n    return results\n  } catch (error) {\n    console.error('❌ Schema validation error:', error)\n    return {\n      postsTable: false,\n      usersTable: false,\n      requiredColumns: false\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGO,MAAM,yBAAyB;IACpC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,sDAAsD;QACtD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,SACP,KAAK,CAAC;QAET,IAAI,OAAO;YACT,8EAA8E;YAC9E,IAAI,MAAM,IAAI,KAAK,cAAc,MAAM,OAAO,CAAC,QAAQ,CAAC,mBAAmB;gBACzE,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;YACA,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,0BAA0B;QACtC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,MAAM,sBAAsB;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,0CAA0C;QAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,WAAW;QAE1D,IAAI,OAAO;YACT,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,sBAAsB,KAAK,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI;QAChE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB;IAC9B,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,8CAA8C;QAC9C,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;QAEnE,IAAI,OAAO;YACT,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,oBAAoB,UAAU,kBAAkB;QAC5D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAGO,MAAM,0BAA0B;IAMrC,QAAQ,GAAG,CAAC;IAEZ,MAAM,UAAU;QACd,UAAU,MAAM;QAChB,SAAS,MAAM;QACf,MAAM,MAAM;QACZ,SAAS;IACX;IAEA,QAAQ,OAAO,GAAG,QAAQ,QAAQ,IAAI,QAAQ,OAAO,IAAI,QAAQ,IAAI;IAErE,IAAI,QAAQ,OAAO,EAAE;QACnB,QAAQ,GAAG,CAAC;IACd,OAAO;QACL,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,eAAe,QAAQ,QAAQ,GAAG,MAAM;QACpD,QAAQ,GAAG,CAAC,cAAc,QAAQ,OAAO,GAAG,MAAM;QAClD,QAAQ,GAAG,CAAC,WAAW,QAAQ,IAAI,GAAG,MAAM;IAC9C;IAEA,OAAO;AACT;AAGO,MAAM,oBAAoB;IAC/B,QAAQ,GAAG,CAAC;IACZ,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;IAEhB,MAAM,cAAc,MAAM;IAE1B,IAAI,CAAC,aAAa;QAChB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;IACd;IAEA,OAAO;AACT;AAGO,MAAM,yBAAyB;IAKpC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,uDAAuD;QACvD,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,SACL,MAAM,CAAC,2FACP,KAAK,CAAC;QAET,8BAA8B;QAC9B,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,SACL,MAAM,CAAC,uBACP,KAAK,CAAC;QAET,MAAM,UAAU;YACd,YAAY,CAAC;YACb,YAAY,CAAC;YACb,iBAAiB,CAAC,cAAc,cAAc;QAChD;QAEA,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,kBAAkB,QAAQ,UAAU,GAAG,MAAM;QACzD,QAAQ,GAAG,CAAC,kBAAkB,QAAQ,UAAU,GAAG,MAAM;QACzD,QAAQ,GAAG,CAAC,uBAAuB,QAAQ,eAAe,GAAG,MAAM;QAEnE,IAAI,CAAC,QAAQ,UAAU,IAAI,CAAC,QAAQ,UAAU,EAAE;YAC9C,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YACL,YAAY;YACZ,YAAY;YACZ,iBAAiB;QACnB;IACF;AACF", "debugId": null}}]}