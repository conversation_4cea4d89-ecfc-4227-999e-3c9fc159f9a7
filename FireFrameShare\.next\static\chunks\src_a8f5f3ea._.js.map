{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/hooks/use-toast.ts"], "sourcesContent": ["\"use client\"\r\n\r\n// Inspired by react-hot-toast library\r\nimport * as React from \"react\"\r\n\r\nimport type {\r\n  ToastActionElement,\r\n  ToastProps,\r\n} from \"@/components/ui/toast\"\r\n\r\nconst TOAST_LIMIT = 1\r\nconst TOAST_REMOVE_DELAY = 1000000\r\n\r\ntype ToasterToast = ToastProps & {\r\n  id: string\r\n  title?: React.ReactNode\r\n  description?: React.ReactNode\r\n  action?: ToastActionElement\r\n}\r\n\r\nconst actionTypes = {\r\n  ADD_TOAST: \"ADD_TOAST\",\r\n  UPDATE_TOAST: \"UPDATE_TOAST\",\r\n  DISMISS_TOAST: \"DISMISS_TOAST\",\r\n  REMOVE_TOAST: \"REMOVE_TOAST\",\r\n} as const\r\n\r\nlet count = 0\r\n\r\nfunction genId() {\r\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\r\n  return count.toString()\r\n}\r\n\r\ntype ActionType = typeof actionTypes\r\n\r\ntype Action =\r\n  | {\r\n      type: ActionType[\"ADD_TOAST\"]\r\n      toast: ToasterToast\r\n    }\r\n  | {\r\n      type: ActionType[\"UPDATE_TOAST\"]\r\n      toast: Partial<ToasterToast>\r\n    }\r\n  | {\r\n      type: ActionType[\"DISMISS_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n  | {\r\n      type: ActionType[\"REMOVE_TOAST\"]\r\n      toastId?: ToasterToast[\"id\"]\r\n    }\r\n\r\ninterface State {\r\n  toasts: ToasterToast[]\r\n}\r\n\r\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\r\n\r\nconst addToRemoveQueue = (toastId: string) => {\r\n  if (toastTimeouts.has(toastId)) {\r\n    return\r\n  }\r\n\r\n  const timeout = setTimeout(() => {\r\n    toastTimeouts.delete(toastId)\r\n    dispatch({\r\n      type: \"REMOVE_TOAST\",\r\n      toastId: toastId,\r\n    })\r\n  }, TOAST_REMOVE_DELAY)\r\n\r\n  toastTimeouts.set(toastId, timeout)\r\n}\r\n\r\nexport const reducer = (state: State, action: Action): State => {\r\n  switch (action.type) {\r\n    case \"ADD_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\r\n      }\r\n\r\n    case \"UPDATE_TOAST\":\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\r\n        ),\r\n      }\r\n\r\n    case \"DISMISS_TOAST\": {\r\n      const { toastId } = action\r\n\r\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\r\n      // but I'll keep it here for simplicity\r\n      if (toastId) {\r\n        addToRemoveQueue(toastId)\r\n      } else {\r\n        state.toasts.forEach((toast) => {\r\n          addToRemoveQueue(toast.id)\r\n        })\r\n      }\r\n\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.map((t) =>\r\n          t.id === toastId || toastId === undefined\r\n            ? {\r\n                ...t,\r\n                open: false,\r\n              }\r\n            : t\r\n        ),\r\n      }\r\n    }\r\n    case \"REMOVE_TOAST\":\r\n      if (action.toastId === undefined) {\r\n        return {\r\n          ...state,\r\n          toasts: [],\r\n        }\r\n      }\r\n      return {\r\n        ...state,\r\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\r\n      }\r\n  }\r\n}\r\n\r\nconst listeners: Array<(state: State) => void> = []\r\n\r\nlet memoryState: State = { toasts: [] }\r\n\r\nfunction dispatch(action: Action) {\r\n  memoryState = reducer(memoryState, action)\r\n  listeners.forEach((listener) => {\r\n    listener(memoryState)\r\n  })\r\n}\r\n\r\ntype Toast = Omit<ToasterToast, \"id\">\r\n\r\nfunction toast({ ...props }: Toast) {\r\n  const id = genId()\r\n\r\n  const update = (props: ToasterToast) =>\r\n    dispatch({\r\n      type: \"UPDATE_TOAST\",\r\n      toast: { ...props, id },\r\n    })\r\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\r\n\r\n  dispatch({\r\n    type: \"ADD_TOAST\",\r\n    toast: {\r\n      ...props,\r\n      id,\r\n      open: true,\r\n      onOpenChange: (open) => {\r\n        if (!open) dismiss()\r\n      },\r\n    },\r\n  })\r\n\r\n  return {\r\n    id: id,\r\n    dismiss,\r\n    update,\r\n  }\r\n}\r\n\r\nfunction useToast() {\r\n  const [state, setState] = React.useState<State>(memoryState)\r\n\r\n  React.useEffect(() => {\r\n    listeners.push(setState)\r\n    return () => {\r\n      const index = listeners.indexOf(setState)\r\n      if (index > -1) {\r\n        listeners.splice(index, 1)\r\n      }\r\n    }\r\n  }, [state])\r\n\r\n  return {\r\n    ...state,\r\n    toast,\r\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\r\n  }\r\n}\r\n\r\nexport { useToast, toast }\r\n"], "names": [], "mappings": ";;;;;AAEA,sCAAsC;AACtC;;AAHA;;AAUA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;8BAAE;YACd,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,QAAQ,CAAC,GAAG;wBACd,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF;GAlBS", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ToastProvider = ToastPrimitives.Provider\r\n\r\nconst ToastViewport = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Viewport\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\r\n\r\nconst toastVariants = cva(\r\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"border bg-background text-foreground\",\r\n        destructive:\r\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Toast = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\r\n    VariantProps<typeof toastVariants>\r\n>(({ className, variant, ...props }, ref) => {\r\n  return (\r\n    <ToastPrimitives.Root\r\n      ref={ref}\r\n      className={cn(toastVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nToast.displayName = ToastPrimitives.Root.displayName\r\n\r\nconst ToastAction = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Action>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Action\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nToastAction.displayName = ToastPrimitives.Action.displayName\r\n\r\nconst ToastClose = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Close>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Close\r\n    ref={ref}\r\n    className={cn(\r\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\r\n      className\r\n    )}\r\n    toast-close=\"\"\r\n    {...props}\r\n  >\r\n    <X className=\"h-4 w-4\" />\r\n  </ToastPrimitives.Close>\r\n))\r\nToastClose.displayName = ToastPrimitives.Close.displayName\r\n\r\nconst ToastTitle = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Title>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Title\r\n    ref={ref}\r\n    className={cn(\"text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastTitle.displayName = ToastPrimitives.Title.displayName\r\n\r\nconst ToastDescription = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Description>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm opacity-90\", className)}\r\n    {...props}\r\n  />\r\n))\r\nToastDescription.displayName = ToastPrimitives.Description.displayName\r\n\r\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\r\n\r\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\r\n\r\nexport {\r\n  type ToastProps,\r\n  type ToastActionElement,\r\n  ToastProvider,\r\n  ToastViewport,\r\n  Toast,\r\n  ToastTitle,\r\n  ToastDescription,\r\n  ToastClose,\r\n  ToastAction,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,gBAAgB,oKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG,oKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,6LAAC,oKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;;AACA,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,oKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/toaster.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useToast } from \"@/hooks/use-toast\"\r\nimport {\r\n  Toast,\r\n  ToastClose,\r\n  ToastDescription,\r\n  ToastProvider,\r\n  ToastTitle,\r\n  ToastViewport,\r\n} from \"@/components/ui/toast\"\r\n\r\nexport function Toaster() {\r\n  const { toasts } = useToast()\r\n\r\n  return (\r\n    <ToastProvider>\r\n      {toasts.map(function ({ id, title, description, action, ...props }) {\r\n        return (\r\n          <Toast key={id} {...props}>\r\n            <div className=\"grid gap-1\">\r\n              {title && <ToastTitle>{title}</ToastTitle>}\r\n              {description && (\r\n                <ToastDescription>{description}</ToastDescription>\r\n              )}\r\n            </div>\r\n            {action}\r\n            <ToastClose />\r\n          </Toast>\r\n        )\r\n      })}\r\n      <ToastViewport />\r\n    </ToastProvider>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAYO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,6LAAC,oIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACE,6LAAC,oIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,6LAAC;4BAAI,WAAU;;gCACZ,uBAAS,6LAAC,oIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,6LAAC,oIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,6LAAC,oIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB;GAtBgB;;QACK,+HAA<PERSON>,CAAA,WAAQ;;;KADb", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from \"@supabase/supabase-js\";\n\n// Supabase configuration\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;\n\n// Debug environment variables (remove after testing)\nconsole.log(\"🔍 Environment Debug:\");\nconsole.log(\"- URL:\", supabaseUrl ? \"✅ Loaded\" : \"❌ Missing\");\nconsole.log(\"- Anon Key:\", supabaseAnonKey ? \"✅ Loaded\" : \"❌ Missing\");\nconsole.log(\"- URL Value:\", supabaseUrl);\nconsole.log(\n  \"- Anon Key (first 20 chars):\",\n  supabaseAnonKey?.substring(0, 20) + \"...\"\n);\n\n// Validate environment variables\nif (!supabaseUrl) {\n  throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\n\nif (!supabaseAnonKey) {\n  throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\n\n// Create Supabase client for client-side operations\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey, {\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n  },\n  realtime: {\n    params: {\n      eventsPerSecond: 10,\n    },\n  },\n});\n\n// Create Supabase client with service role for server-side operations\n// Only use this for server-side operations that require elevated permissions\nexport const supabaseAdmin =\n  typeof window === \"undefined\" && supabaseServiceRoleKey\n    ? createClient(supabaseUrl, supabaseServiceRoleKey, {\n        auth: {\n          autoRefreshToken: false,\n          persistSession: false,\n        },\n      })\n    : null;\n\n// Database and Storage references for convenience\nexport const db = supabase;\nexport const storage = supabase.storage;\n\n// Log configuration (without sensitive data)\nexport const logSupabaseConfig = () => {\n  console.log(\"Supabase Configuration:\");\n  console.log(\"- URL:\", supabaseUrl);\n  console.log(\"- Environment:\", process.env.NODE_ENV);\n  console.log(\"- Client initialized:\", !!supabase);\n};\n\n// Initialize Supabase debugging\nexport const initSupabase = () => {\n  console.log(\"🚀 Supabase initialized\");\n  logSupabaseConfig();\n\n  // Test connection on initialization\n  if (typeof window !== \"undefined\") {\n    console.log(\"Client-side Supabase ready\");\n  } else {\n    console.log(\"Server-side Supabase ready\");\n  }\n};\n\nexport default supabase;\n"], "names": [], "mappings": ";;;;;;;;;AAGoB;AAHpB;;AAEA,yBAAyB;AACzB,MAAM;AACN,MAAM;AACN,MAAM,yBAAyB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB;AAEpE,qDAAqD;AACrD,QAAQ,GAAG,CAAC;AACZ,QAAQ,GAAG,CAAC,UAAU,uCAAc;AACpC,QAAQ,GAAG,CAAC,eAAe,uCAAkB;AAC7C,QAAQ,GAAG,CAAC,gBAAgB;AAC5B,QAAQ,GAAG,CACT,gCACA,iBAAiB,UAAU,GAAG,MAAM;AAGtC,iCAAiC;AACjC,uCAAkB;;AAElB;AAEA,uCAAsB;;AAEtB;AAGO,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACjE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,UAAU;QACR,QAAQ;YACN,iBAAiB;QACnB;IACF;AACF;AAIO,MAAM,gBACX,6EAOI;AAGC,MAAM,KAAK;AACX,MAAM,UAAU,SAAS,OAAO;AAGhC,MAAM,oBAAoB;IAC/B,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,UAAU;IACtB,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,yBAAyB,CAAC,CAAC;AACzC;AAGO,MAAM,eAAe;IAC1B,QAAQ,GAAG,CAAC;IACZ;IAEA,oCAAoC;IACpC,wCAAmC;QACjC,QAAQ,GAAG,CAAC;IACd,OAAO;;IAEP;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/auth/auth-provider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { createContext, useContext, useEffect, useState } from 'react';\nimport { supabase } from '@/lib/supabase';\nimport { Session, User as SupabaseUser } from '@supabase/supabase-js';\nimport { User } from '@/lib/types';\n\ninterface AuthContextType {\n  session: Session | null;\n  user: User | null;\n  isLoading: boolean;\n  signOut: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType>({\n  session: null,\n  user: null,\n  isLoading: true,\n  signOut: async () => {},\n});\n\nexport const useAuthContext = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuthContext must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Helper function to fetch user profile from database\nconst fetchUserProfile = async (supabaseUser: SupabaseUser): Promise<User | null> => {\n  const { data, error } = await supabase\n    .from('users')\n    .select('*')\n    .eq('id', supabaseUser.id)\n    .single();\n\n  if (error) {\n    console.error('Error fetching user profile:', error);\n    return null;\n  }\n\n  return {\n    id: data.id,\n    username: data.username,\n    email: data.email,\n    avatarUrl: data.avatar_url,\n    bio: data.bio,\n    contacts: {\n      website: { \n        value: data.website_url || '', \n        isPublic: data.website_public || false \n      },\n      phone: { \n        value: data.phone || '', \n        isPublic: data.phone_public || false \n      },\n      messaging: { \n        platform: data.messaging_platform || '', \n        username: data.messaging_username || '', \n        isPublic: data.messaging_public || false \n      },\n    },\n  };\n};\n\nexport default function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [session, setSession] = useState<Session | null>(null);\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession();\n      \n      if (session?.user) {\n        const userProfile = await fetchUserProfile(session.user);\n        setSession(session);\n        setUser(userProfile);\n      }\n      \n      setIsLoading(false);\n    };\n\n    getInitialSession();\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        console.log('Auth state changed:', event, session);\n        \n        if (session?.user) {\n          const userProfile = await fetchUserProfile(session.user);\n          setSession(session);\n          setUser(userProfile);\n        } else {\n          setSession(null);\n          setUser(null);\n        }\n        \n        setIsLoading(false);\n      }\n    );\n\n    return () => {\n      subscription.unsubscribe();\n    };\n  }, []);\n\n  const signOut = async () => {\n    setIsLoading(true);\n    await supabase.auth.signOut();\n    setSession(null);\n    setUser(null);\n    setIsLoading(false);\n  };\n\n  const value = {\n    session,\n    user,\n    isLoading,\n    signOut,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAcA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAmB;IACjD,SAAS;IACT,MAAM;IACN,WAAW;IACX,SAAS,WAAa;AACxB;AAEO,MAAM,iBAAiB;;IAC5B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQb,sDAAsD;AACtD,MAAM,mBAAmB,OAAO;IAC9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;IAEA,OAAO;QACL,IAAI,KAAK,EAAE;QACX,UAAU,KAAK,QAAQ;QACvB,OAAO,KAAK,KAAK;QACjB,WAAW,KAAK,UAAU;QAC1B,KAAK,KAAK,GAAG;QACb,UAAU;YACR,SAAS;gBACP,OAAO,KAAK,WAAW,IAAI;gBAC3B,UAAU,KAAK,cAAc,IAAI;YACnC;YACA,OAAO;gBACL,OAAO,KAAK,KAAK,IAAI;gBACrB,UAAU,KAAK,YAAY,IAAI;YACjC;YACA,WAAW;gBACT,UAAU,KAAK,kBAAkB,IAAI;gBACrC,UAAU,KAAK,kBAAkB,IAAI;gBACrC,UAAU,KAAK,gBAAgB,IAAI;YACrC;QACF;IACF;AACF;AAEe,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IAC9E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;oBAE5D,IAAI,SAAS,MAAM;wBACjB,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;wBACvD,WAAW;wBACX,QAAQ;oBACV;oBAEA,aAAa;gBACf;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,QAAQ,GAAG,CAAC,uBAAuB,OAAO;oBAE1C,IAAI,SAAS,MAAM;wBACjB,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;wBACvD,WAAW;wBACX,QAAQ;oBACV,OAAO;wBACL,WAAW;wBACX,QAAQ;oBACV;oBAEA,aAAa;gBACf;;YAGF;0CAAO;oBACL,aAAa,WAAW;gBAC1B;;QACF;iCAAG,EAAE;IAEL,MAAM,UAAU;QACd,aAAa;QACb,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC3B,WAAW;QACX,QAAQ;QACR,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAhEwB;KAAA", "debugId": null}}]}