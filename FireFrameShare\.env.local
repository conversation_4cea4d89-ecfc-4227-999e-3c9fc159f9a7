# Supabase Configuration
# Replace these placeholder values with your actual Supabase project credentials
# You can find these in your Supabase project dashboard under Settings > API

# Your Supabase project URL (e.g., https://your-project-id.supabase.co)
NEXT_PUBLIC_SUPABASE_URL="https://gtsbnkuehpijzmlfupqk.supabase.co"

# Your Supabase anonymous/public key (safe to use in client-side code)
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd0c2Jua3VlaHBpanptbGZ1cHFrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUyMTM0NzUsImV4cCI6MjA3MDc4OTQ3NX0.RobiamNJbAyqjpbbS4zJ9534IGZLGLwS-hhzxl-B25w"

# Your Supabase service role key (keep this secret, server-side only)
SUPABASE_SERVICE_ROLE_KEY="PZofHe05b25jvmeHoAv4OjWLO3UixwwB5LeHIgOdei440kUSG2Mgud2dDzMNiOa2mbIbG5XTpgGIVQedRNDsbw=="

# Gemini LLM key for Gemini CLI
GEMINI_API_KEY=AIzaSyBaMIw5uzeYdAzX1UGVuRZHcOFte3pjdLU

# Optional: If you're using Supabase local development
# NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_local_anon_key

